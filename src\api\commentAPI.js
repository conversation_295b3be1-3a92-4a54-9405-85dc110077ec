import api from "../configs/api";

// API để lấy tóm tắt thống kê blog (chỉ lấy commentCount)
export const fetchBlogSummary = async () => {
  try {
    console.log(`📊 Fetching blog summary statistics...`);
    console.log(`📊 API endpoint: GET /blog/summary`);

    const response = await api.get("/blog/summary");

    console.log(`✅ Blog summary API call success:`, response.data);
    return response;
  } catch (error) {
    console.error(`❌ fetchBlogSummary API error:`, error);
    console.error(`❌ Error response:`, error.response?.data);
    console.error(`❌ Error status:`, error.response?.status);
    console.error(`❌ Error message:`, error.message);

    // Handle specific error cases
    if (error.response?.status === 404) {
      throw new Error(`<PERSON>hông tìm thấy thống kê blog.`);
    } else if (error.response?.status === 500) {
      throw new Error(`Lỗi server khi lấy thống kê blog.`);
    } else {
      throw new Error(`<PERSON><PERSON><PERSON>ng thể lấy thống kê blog. Vui lòng thử lại sau.`);
    }
  }
};
