/* ===== HERO SECTION STYLES ===== */
.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  margin-top: -40px;
}

.hero-slider {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.hero-inner {
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.hero-slide {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0;
  transition: opacity var(--transition-slow, 0.8s ease);
  display: flex;
  align-items: center;
}

.hero-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Giảm opacity để ảnh sáng hơn */
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.08));
  z-index: 1;
}

.hero-slide.active {
  opacity: 1;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  padding: 0 var(--spacing-lg, 1.5rem);
  color: var(--text-white, #fff);

}
  

.hero-content h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: var(--spacing-lg, 1.5rem);

}

.hero-content p {
  font-size: 18px;
  margin-bottom: var(--spacing-xl, 2rem);
}

.btn-hero {
  background: var(--gradient-primary, linear-gradient(135deg, #ff5a7d, #ff847c));
  padding: var(--spacing-md, 0.75rem) var(--spacing-2xl, 2.5rem);
  font-size: 16px;
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow-lg, 0 6px 15px rgba(0, 0, 0, 0.2));
  color: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  cursor: pointer;
  margin-top: 170px;
}

.btn-hero:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(255, 90, 125, 0.3);
}

/* ===== SLIDER CONTROLS ===== */
.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--text-white, #fff);
  font-size: 20px;
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full, 50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal, 0.3s ease);
  backdrop-filter: blur(10px);
}

.slider-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%) scale(1.1);
}

.slider-nav.prev {
  left: var(--spacing-xl, 2rem);
}

.slider-nav.next {
  right: var(--spacing-xl, 2rem);
}

.slider-dots {
  position: absolute;
  bottom: var(--spacing-xl, 2rem);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  z-index: 2;
  gap: var(--spacing-md, 1rem);
}

.dot {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full, 50%);
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all var(--transition-normal, 0.3s ease);
  backdrop-filter: blur(5px);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

.dot.active {
  background: var(--gradient-primary, linear-gradient(135deg, #ff5a7d, #ff847c));
  border-color: var(--text-white, #fff);
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(255, 90, 125, 0.5);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 992px) {
  .hero-content h1 {
    font-size: 36px;
  }

  .slider-nav {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .hero {
    height: 400px;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .hero-content p {
    font-size: 16px;
  }

  .slider-nav {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .slider-nav.prev {
    left: var(--spacing-lg, 1.25rem);
  }

  .slider-nav.next {
    right: var(--spacing-lg, 1.25rem);
  }

  .dot {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 576px) {
  .hero {
    height: 350px;
  }

  .hero-content h1 {
    font-size: 28px;
    margin-bottom: var(--spacing-md, 1rem);
  }

  .hero-content p {
    font-size: 14px;
    margin-bottom: var(--spacing-lg, 1.5rem);
  }

  .slider-nav {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .dot {
    width: 12px;
    height: 12px;
  }

  .slider-dots {
    gap: var(--spacing-sm, 0.5rem);
  }

  .btn btn-hero {
    font-size: 14px;
    padding: var(--spacing-sm, 0.5rem) var(--spacing-xl, 2rem);
    border-radius: var(--radius-md, 8px);
    margin-top: 100px;
    margin-left: 200px;
  }
}
.hero-fadeout {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 48px; /* hoặc chiều cao bạn muốn */
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, #f6f8ff 100%);
  z-index: 10;
  pointer-events: none;
}


