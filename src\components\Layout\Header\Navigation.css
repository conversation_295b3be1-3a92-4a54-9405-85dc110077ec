/* ===== NAVIGATION STYLES ===== */
/* Color Palette:
   - Xanh đậm 1 (đậm nhất): #2753d0
   - Xanh đậm 2: #086ce4
   - Xanh đậm 3 (nh<PERSON><PERSON> nhất): #0283f5
*/

.main-nav {
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  justify-content: center;
  gap: var(--spacing-xl);
}

.nav-item {
  position: relative;
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 15px;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  position: relative;
  display: block;
  font-size: 16px;
}

.nav-link.active {
  color: #2753d0; /* Xanh đậm 1 - đậm nhất */
  font-weight: 600;
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 65px;
  height: 3px;
  background: linear-gradient(90deg, #2753d0 0%, #086ce4 50%, #0283f5 100%);
  border-radius: var(--radius-sm);
}

/* ===== DROPDOWN FIXED ===== */
.nav-item.has-dropdown {
  position: relative;
}

.dropdown-menu {
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out,
    visibility 0.3s ease-out;
  position: absolute;
  left: 0;
  top: 100%;
  min-width: 240px;
  padding: 0;
  border-radius: 12px;
  background: linear-gradient(to bottom, #ffffff 0%, rgb(255, 255, 255) 100%);
  box-shadow: 0 4px 20px rgba(39, 83, 208, 0.15);
  animation: fadeSlideIn 0.4s ease;
  overflow: hidden;
  z-index: 100;
  transform-origin: top center;
}

/* Optional: Add a hover effect for items inside dropdown */
.dropdown-menu li {
  transition: background 0.2s, color 0.2s;
}

.dropdown-menu li:hover {
  background: white; /* Xanh đậm 1 với opacity */
  color: #000000; /* Xanh đậm 1 */
}

.nav-item.has-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-link {
  display: block;
  padding: 12px 20px;
  color: #000000; /* Xanh đậm 1 */
  text-decoration: none;
  font-size: 15px;
  font-weight: 600;
  transition: background 0.2s, color 0.2s;
}

.dropdown-link:hover {
  background: #0283f580; /* Xanh đậm 2 với opacity */
  color: #000000; /* Xanh đậm 2 */
}

/* ===== ADMIN NAVIGATION STYLES ===== */
.nav-link.admin-nav {
  background: linear-gradient(135deg, #2753d0 0%, #086ce4 100%);
  color: white;
  font-weight: 600;
  border-radius: 6px;
  margin-left: 8px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.nav-link.admin-nav:hover {
  background: linear-gradient(135deg, #1a4bb8 0%, #0659cc 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(39, 83, 208, 0.3);
}

.nav-link.admin-nav.active {
  background: linear-gradient(135deg, #1a4bb8 0%, #0659cc 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(39, 83, 208, 0.4);
}

.nav-link.admin-nav.active::after {
  display: none; /* Hide the underline for admin nav items */
}

/* Admin navigation items spacing */
.nav-item.admin-item {
  margin-left: 4px;
}

.nav-item.admin-item:first-of-type {
  margin-left: 16px;
}
