/* WriteBlogs.css */

/* Status Styles */
.blog-status {
  font-weight: bold;
}

.blog-status.published {
  color: #52c41a;
}

.blog-status.draft {
  color: #fa8c16;
}

.blog-status.pending {
  color: #1890ff;
}

.blog-status.approved {
  color: #722ed1;
}

.blog-status.rejected {
  color: #ff4d4f;
}

.blog-status.archived {
  color: #8c8c8c;
}

/* Table Styles */
.blog-title-cell {
  font-weight: bold;
  margin-bottom: 4px;
}

.blog-id-cell {
  font-size: 12px;
  color: #666;
}

.blog-date-cell {
  font-size: 13px;
}

.blog-stats-cell {
  font-size: 12px;
  margin-bottom: 2px;
}

.blog-stats-likes {
  font-size: 12px;
}

.blog-tag-primary {
  font-size: 11px;
}

.blog-tag-empty {
  color: #999;
  font-size: 11px;
}

.blog-tag-count {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.stats-card.total {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.stats-card.published {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.stats-card.draft {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.stats-card.views {
  background: #f9f0ff;
  border: 1px solid #d3adf7;
}

.stats-card.likes {
  background: #fff0f6;
  border: 1px solid #ffadd2;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
}

.stats-number.total {
  color: #52c41a;
}

.stats-number.published {
  color: #1890ff;
}

.stats-number.draft {
  color: #fa8c16;
}

.stats-number.views {
  color: #722ed1;
}

.stats-number.likes {
  color: #eb2f96;
}

.stats-label {
  color: #666;
  font-size: 14px;
}

/* Filter and Actions */
.filter-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-select {
  width: 200px;
}

.filter-actions > div {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* Form Styles */
.image-upload-input {
  margin-bottom: 8px;
}

.image-upload-hint {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Modal Content */
.blog-detail-item {
  margin-bottom: 12px;
}

.blog-image {
  max-width: 100%;
  margin-top: 4px;
  margin-bottom: 12px;
}

.blog-content {
  white-space: pre-line;
}

/* Tag Management */
.tag-create-button {
  margin-bottom: 16px;
}