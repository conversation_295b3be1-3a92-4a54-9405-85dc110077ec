.blog-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0;
  background-color: #ffffff;
  border-radius: 0;
  box-shadow: none;
}

.blog-header {
  margin-bottom: 2rem;
}

.blog-image {
  width: 100%;
  height: 350px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  object-position: center top;
}

.blog-title {
  font-family: 'Arial', sans-serif;
  font-size: 2.6rem;
  font-weight: 700;
  color: #0d47a1;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  line-height: 1.2;
}

.blog-meta {
  font-family: 'Arial', sans-serif;
  font-size: 0.9rem;
  color: #333333;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* Blog Stats */
.blog-stats {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 2.5rem;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

.blog-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.95rem;
  font-weight: 500;
}

.blog-stats .stat-icon {
  font-size: 1.1rem;
}

.blog-stats .stat-count {
  font-weight: 600;
  color: #333;
}

.blog-stats .like-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.blog-stats .like-button:hover {
  background-color: #ffe6e6;
  transform: scale(1.05);
}

.blog-stats .like-button:active {
  transform: scale(0.95);
}

.blog-stats .like-button.liking {
  opacity: 0.6;
  cursor: not-allowed;
}

.blog-stats .like-button.liking .stat-icon {
  animation: heartbeat-detail 0.6s ease-in-out;
}

@keyframes heartbeat-detail {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #cceeff;
}

.blog-content {
  font-family: 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #333333;
  margin-top: 2rem;
}

.blog-content h2 {
  font-family: 'Arial', sans-serif;
  font-size: 1.8rem;
  color: #000000;
  margin: 2rem 0 1rem;
  font-weight: 700;
}

.blog-content h3 {
  font-family: 'Arial', sans-serif;
  font-size: 1.4rem;
  color: #000000;
  margin: 1.5rem 0 1rem;
  font-weight: 700;
}

.blog-content p {
  margin-bottom: 1.2rem;
}

.blog-content ul, .blog-content ol {
  margin: 1rem 0;
  padding-left: 2.2rem;
}

.blog-content li {
  margin-bottom: 0.6rem;
}

.back-button {
  background-color: #4299e1;
  color: white;
  border-radius: 9999px;
  font-weight: 600;
  transition: background-color 0.3s ease-in-out;
  margin-top: 2.5rem;
}

.back-button:hover {
  background-color: #3182ce;
}

.slogan-section {
  background-color: #ffffff;
  padding: 2rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
  border-radius: 0;
  box-shadow: none;
}

.slogan-title {
  font-family: 'Arial', sans-serif;
  font-size: 2.8rem;
  font-weight: 800;
  color: #1a365d;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  letter-spacing: -0.05em;
}

.slogan-text {
  font-family: 'Arial', sans-serif;
  font-size: 1.8rem;
  color: #D00000;
  font-weight: 700;
  font-style: italic;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.top-header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-logo {
  font-weight: bold;
  color: #0d47a1;
  font-size: 1.2rem;
}

.breadcrumbs {
  font-size: 0.9rem;
  color: #606060;
}

.breadcrumbs a {
  color: #606060;
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
}

.callout-box {
  background-color: #ffffff;
  border-left: none;
  padding: 1rem 1.5rem;
  margin: 2rem auto;
  max-width: 900px;
  border-radius: 0;
  box-shadow: none;
  color: #424242;
  font-size: 0.95rem;
  line-height: 1.5;
}

.callout-box strong {
  color: #0d47a1;
}

.blog-detail-page-wrapper {
  background-color: #ffffff;
  padding-bottom: 4rem;
}

/* Related Articles Section Styles */
.related-articles-section {
  max-width: 900px;
  margin: 4rem auto 2rem;
  padding: 0;
}

.related-articles-header {
  display: flex;
  align-items: center;
  margin-bottom: 2.5rem;
}

.related-articles-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #007bff;
  margin-right: 1.5rem;
  white-space: nowrap;
  font-family: 'Arial', sans-serif;
}

.related-articles-divider {
  flex-grow: 1;
  height: 2px;
  background-color: #a7d9f7;
}

.articles-grid {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding-bottom: 1rem;
}

.articles-grid::-webkit-scrollbar {
  display: none;
}

.article-card {
  flex-shrink: 0;
  width: 250px;
  max-width: 250px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.article-card-image-wrapper {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.article-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.medpro-logo-overlay {
  /* Removed all styles for medpro-logo-overlay */
}

.article-card-content {
  padding: 1.2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.article-category {
  font-size: 0.85rem;
  color: #ff9800;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  font-family: 'Helvetica Neue', sans-serif;
}

.article-category::before {
  content: '•';
  color: #ff9800;
  font-size: 1.2em;
  margin-right: 0.4em;
  line-height: 1;
}

.article-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 0.8rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Arial', sans-serif;
  text-decoration: none;
}

.article-card-meta {
  font-size: 0.8rem;
  color: #757575;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-family: 'Arial', sans-serif;
}

.article-card-meta img {
  width: 14px;
  height: 14px;
  margin-right: 0.2rem;
}

.read-more-link {
  font-size: 0.9rem;
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin-top: auto;
  font-family: 'Helvetica Neue', sans-serif;
}

.read-more-link:hover {
  text-decoration: none;
}

.read-more-link svg {
  margin-left: 0.3em;
  width: 1em;
  height: 1em;
}

.view-all-button-container {
  text-align: center;
  margin-top: 2rem;
  margin-bottom: 4rem;
}

.view-all-button {
  display: inline-flex;
  align-items: center;
  padding: 0.8rem 2rem;
  background-color: #007bff;
  color: white;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-family: 'Arial', sans-serif;
}

.view-all-button:hover {
  background-color: #0056b3;
}

.view-all-button svg {
  margin-left: 0.75rem;
  font-size: 1.1rem;
  stroke: white;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
}

.pagination-button {
  background-color: #e2e8f0;
  color: #2d3748;
  border: 1px solid #cbd5e0;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.pagination-button:hover {
  background-color: #cbd5e0;
}

.pagination-button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

@media (max-width: 768px) {
  .top-header-container {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  .slogan-section {
    padding: 0.05rem 1.5rem;
    border-radius: 30px;
  }
  .slogan-text {
    font-size: 1.4rem;
  }
  .callout-box {
    margin: 1.5rem 1rem;
    padding: 0.8rem 1rem;
  }
  .blog-title {
    font-size: 2rem;
  }
  .blog-image {
    height: 250px;
  }
  .blog-detail-container {
    padding: 1.5rem;
  }
  .related-articles-section {
    padding: 0 1rem;
  }
  .related-articles-title {
    font-size: 1.5rem;
  }
  .articles-grid {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }
  .article-card {
    flex: 1 1 180px;
    max-width: unset;
  }
  .article-card-image {
    height: 150px;
  }
  .article-card-content {
    padding: 1rem;
  }
  .article-card-title {
    font-size: 1rem;
  }
  .view-all-button {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
}