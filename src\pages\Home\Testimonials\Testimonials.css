/* ========== DOCTORS SECTION ========== */
.doctors-section {
  background-color: #f7fbfd;
  padding: 64px 0;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 12px;
  background: linear-gradient(to right, #0288d1, #00bcd4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.section-subtitle-description {
  text-align: center;
  font-size: 1rem;
  font-weight: 400;
  color: #64748b;
  margin-bottom: 36px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* ========== SPLIDE STYLES ========== */
.splide__slide {
  display: flex;
  justify-content: center;
}

/* ========== DOCTOR CARD ========== */
.doctor-card {
  width: 100%;
  max-width: 300px;
  background: #ffffff;
  border-radius: 16px;
  padding: 24px 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.doctor-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 4px;
  width: 100%;
  /* background: linear-gradient(to right, #00bcd4, #0288d1); */
}

.doctor-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

/* ========== AVATAR ========== */
.doctor-avatar {
  width: 100px;
  height: 100px;
  margin-bottom: 16px;
}

.doctor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10%;
  border: 2px solid #0288d1;
}

/* ========== NAME + TITLE ========== */
.doctor-name {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  margin-bottom: 4px;
}

.doctor-title {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 12px;
  text-align: center;
}

/* ========== DETAILS BADGES ========== */
.doctor-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  width: 100%;
}

.doctor-details div {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #475569;
}

.specialist-badge {
  font-weight: 600;
  color: #0288d1;
}

/* ========== STATS (Đánh giá & Lượt khám) ========== */
.doctor-stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 12px;
  margin-bottom: 16px;
  color: #64748b;
}

.rating-value {
  font-weight: bold;
  color: #f59e0b;
}

.rating-stars {
  display: flex;
  gap: 1px;
  font-size: 12px;
}

.star {
  color: #e2e8f0;
}

.star.filled {
  color: #f59e0b;
}

/* ========== BUTTON ========== */
.consult-btn {
  background: #0283f5;
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  text-align: center;
  transition: background 0.3s ease;
}

.consult-btn:hover {
  background: linear-gradient(to top, #0283f5, #2753d0);
  filter: brightness(1.15);
  transform: translateY(-1px);
  box-shadow: rgba(0, 0, 0, 0.35) 0px -50px 36px -28px inset;
}

/* ========== RESPONSIVE GRID (nếu dùng grid ngoài carousel) ========== */
.doctors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}
