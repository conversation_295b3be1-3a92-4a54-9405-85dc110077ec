.service-detail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  height: auto;
}

.service-info-detail {
  width: 50%;
}

/* <PERSON>ên tr<PERSON>i chứa thông tin dịch vụ */
.service-detail-left {
  flex: 1;
  max-width: calc(100% - 440px);
  /* giữ đủ chỗ cho bên phải */
  min-width: 0;
}

.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

/* <PERSON><PERSON>n ph<PERSON>i là form đặt lịch */
.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

.service-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1e293b;
}

.service-price {
  color: #10b981;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.service-description,
.service-preparation,
.service-detail-list {
  margin-bottom: 24px;
}

.service-details-wrapper {
  padding: 8px 0;
}

.service-description h3,
.prep-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0f172a;
}

.service-description .description {
  margin-bottom: 24px;
  color: #334155;
  line-height: 1.6;
}

.prep-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.prep-item {
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

/* Responsive: Stack dọc trên mobile */
@media (max-width: 768px) {
  .service-detail-container {
    flex-direction: column;
    padding: 16px;
  }

  .service-detail-right {
    width: 100%;
    max-width: 100%;
  }
}

/* Tab items */

.ant-tabs-tab {
  padding: 12px 24px !important;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.ant-tabs-tab-active {
  background-color: #e0f7fa;
  border-radius: 8px;
  color: #0288d1 !important;
}

.ant-tabs-nav,
.ant-tabs-nav-wrap {
  justify-content: flex-start !important;
}

.ant-tabs-ink-bar {
  height: 4px;
  background-color: #0288d1 !important;
  border-radius: 2px;
}

/* Thêm CSS cho phần đánh giá */
.star-rating {
  display: inline-flex;
  margin-right: 8px;
}

.star {
  color: #e0e0e0;
  font-size: 24px;
}

.star.filled {
  color: #FFD700;
}

/* CSS cho danh sách đánh giá */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px 0;
}

.feedback-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.feedback-author {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.feedback-date {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.feedback-comment {
  font-size: 15px;
  color: #374151;
  line-height: 1.6;
  margin: 0;
}